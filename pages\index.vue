<script setup lang="ts">
import { HomeCategories } from '#components'
import type { Banner } from '~/interfaces/banner/banner'

const { t } = useI18n()

const { data, status } = useApi<Banner[]>('banners', { query: { perPage: 2 } })
const loading = computed(() => status.value !== 'success')
const result = computed<Banner[]>(() => data.value as Banner[])
const banners = computed<Banner[]>(() => ([...result.value as Banner[] ?? []]).splice(0, 2))
const url = useRequestURL()

// useRouteCache((helper) => {
// 	helper.setMaxAge(60 /** 60 * 24 */).setCacheable().addTags(['home'])
// })

// useCDNHeaders((helper) => {
// 	helper
// 		.addTags(['home'])
// 		.public()
// 		.setNumeric('maxAge', 21600) // 6hours
// 		.setNumeric('staleIfError', 43200)
// })

useSeoMeta({
	title: () => `${t('header.meta-site-name')} | ${t('header.meta-title')}`,
	description: () => t('header.meta-description'),
	ogTitle: () => `${t('header.meta-site-name')} | ${t('header.meta-title')}`,
	ogDescription: () => t('header.meta-description'),
	twitterTitle: () => `${t('header.meta-site-name')} | ${t('header.meta-title')}`,
	twitterDescription: () => t('header.meta-description'),
})

useHead({
	link: [
		{
			rel: 'canonical',
			href: url.href,
		},
		{
			rel: 'twitter:card',
			href: url.href,
		},
		{
			rel: 'og:url',
			href: url.href,
		},
	],

	script: [
		{
			type: 'application/ld+json',
			innerHTML: JSON.stringify({
				'@context': 'https://schema.org',
				'@type': 'LocalBusiness',
				'name': t('app.action-page-title'),
				'image': 'https://action.jo/images/logo.png',
				'@id': url.href,
				'url': url.href,
				'telephone': '962791009595',
				'priceRange': '10',
				'address': {
					'@type': 'PostalAddress',
					'streetAddress': '329 K. Abdullah I St.',
					'addressLocality': 'Amman',
					'postalCode': '11185',
					'addressCountry': 'JO',
				},
				'geo': {
					'@type': 'GeoCoordinates',
					'latitude': 31.9785307,
					'longitude': 35.9728592,
				},
				'openingHoursSpecification': {
					'@type': 'OpeningHoursSpecification',
					'dayOfWeek': [
						'Monday',
						'Tuesday',
						'Wednesday',
						'Thursday',
						'Friday',
						'Saturday',
						'Sunday',
					],
					'opens': '10:00',
					'closes': '23:00',
				},
				'sameAs': [
					'https://www.facebook.com/Actionmobile11',
					'https://www.instagram.com/actionwebsite/',
					url.href,
				],
			}),
			tagPosition: 'head', // Ensure it's in the <head>
		},
	],
	// @ts-ignore
	__dangerouslyDisableSanitizersByTagID: {
		// Disable sanitization for this script
		'ld-json-schema': ['innerHTML'],
	},
})
</script>

<template>
	<div class="content grid grid-cols-3 gap-6 py-6 max-sm:mx-2">
		<h1 class="hidden">
			{{ $t('app.action-page-title') }}
		</h1>
		<HomeHero />
		<HomeCategories />
		<HomeActions />

		<HomeNewArrival />
		<div class="col-span-3 grid grid-cols-2 gap-6 max-md:grid-cols-1">
			<template v-if="loading">
				<div
					v-for="(_, index) in Array(2)"
					:key="`loading-brand-${index}`"
					class="w-full max-h-72 h-full rounded-lg shadow overflow-hidden"
				>
					<Skeleton class="w-full min-h-32" />
				</div>
			</template>
			<template v-else>
				<NuxtLinkLocale
					v-for="banner in banners"
					:key="banner.bannerId"
					:to="banner.url"
					:title="$t('app.marketing-alt')"
					class="w-full max-h-72 rounded-lg shadow overflow-hidden"
				>
					<NuxtImg
						:src="banner?.media?.image?.preview"
						class="w-full h-full"
						provider="backend"
						:alt="$t('app.marketing-alt')"
						:title="$t('app.marketing-alt')"
						width="867"
						height="272"
						format="webp"
						loading="lazy"
						:sizes="{
							sm: 200,
							md: 418,
							lg: 867,
						}"
					/>
				</NuxtLinkLocale>
			</template>
		</div>
		<NuxtLazyHydrate when-visible>
			<HomeOurChoice />
		</NuxtLazyHydrate>
		<NuxtLazyHydrate when-visible>
			<HomeAnker />
		</NuxtLazyHydrate>
		<NuxtLazyHydrate when-visible>
			<div class="col-span-3 grid grid-cols-4 gap-2 max-md">
				<HomeBestBrands />
				<div class="col-span-1 flex flex-col gap-4 max-sm:col-span-4">
					<HomeDiscount />
					<HomeDelivery />
				</div>
			</div>
		</NuxtLazyHydrate>
		<NuxtLazyHydrate when-idle>
			<HomeFaq />
		</NuxtLazyHydrate>
		<NuxtLazyHydrate when-idle>
			<HomeBestSell />
		</NuxtLazyHydrate>
		<NuxtLazyHydrate when-idle>
			<HomeElectronics />
		</NuxtLazyHydrate>
		<NuxtLazyHydrate when-idle>
			<HomeWearable />
		</NuxtLazyHydrate>
		<NuxtLazyHydrate when-idle>
			<HomeBrandsSlider />
		</NuxtLazyHydrate>
		<NuxtLazyHydrate when-idle>
			<HomeHistory />
		</NuxtLazyHydrate>
		<NuxtLazyHydrate when-idle>
			<HomeAbout />
		</NuxtLazyHydrate>
	</div>
</template>

<style scoped lang="scss">
.float-image {
  :deep(.image img) {
    @apply w-36 absolute left-3 -top-4;
  }
}
</style>
