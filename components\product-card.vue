<script setup lang="ts">
import { useCurrency } from '~/composables/useCurrency'
import type { Details } from '~/interfaces/product/details.js'
import type { NewArrival } from '~/interfaces/product/new-arrival'
import type { Item } from '~/interfaces/product/product-list'

import { useCompareStore } from '~/store/useCompareStore'
import { useFavoriteStore } from '~/store/useFavoriteStore'

const { priceFormat } = useCurrency()
type Variant = 'horizontal' | 'sm' | 'md' | 'lg' | undefined
interface Props {
	product?: Details | NewArrival | Item | undefined
	loading?: boolean
	variant?: Variant
	class?: string
	imageLoading?: 'lazy' | 'eager'
}

const { product, loading, variant = 'md', class: classProp = '', imageLoading = 'lazy' } = defineProps<Props>()
const favoriteStore = useFavoriteStore()
const compareStore = useCompareStore()

const image = computed(() => product?.media?.cover?.[0]?.preview || product?.media?.gallery?.[0]?.preview)
const isOutStock = computed(() => !product?.variance?.stock)
const stockPrice = computed(() => Number(product?.variance?.stock?.price?.value as number))
const offerPrice = computed(() => Number(product?.variance?.stock?.priceBeforeOffer?.value as number))
const isFav = ref(false)
const isCompare = ref(false)
const hasCartConfirm = ref(false)

/** handle on adding item to favorite **/
const onFavorite = async () => {
	if (!isFav.value) {
		return favoriteStore.addToList(product?.productId)
	}

	return favoriteStore.removeFromList(product?.productId)
}

watch(() => favoriteStore.list, () => {
	isFav.value = favoriteStore.hasFav(product?.productId)
}, { immediate: true, deep: true })

watch(() => compareStore.products, () => {
	isCompare.value = compareStore.hasVariance(product?.variance?.varianceId)
}, { immediate: true, deep: true })

/** handle on adding item to cart- **/
const onCompare = async () => {
	if (!isCompare.value) {
		return compareStore.setProduct(product?.variance?.varianceId)
	}

	return compareStore.removeProduct(product?.variance?.varianceId)
}

/** Discount Amount **/
const discountAmount = computed(() => {
	if (!offerPrice?.value || (offerPrice?.value - stockPrice?.value) <= 0) {
		return 0
	}

	return Math.abs(offerPrice?.value - stockPrice?.value)
})

/** Discount Percent **/
const discountPercent = computed(() => {
	if (!discountAmount.value || !!isOutStock.value) {
		return 0
	}

	return ((discountAmount.value / offerPrice.value) * 100).toFixed(0)
})

const isHorizontalCard = computed(() => variant === 'horizontal')
</script>

<template>
	<Card :class="`card-${variant} ${!loading?'group hover:border-primary-600':''} ${classProp}`">
		<CardContent class="p-0">
			<div
				v-if="loading"
				class="space-y-2 p-2 gap-4"
				:class="{ 'flex flex-row': isHorizontalCard }"
			>
				<LazySkeleton
					class="header w-full h-44  rounded-lg"
					:class="{ 'w-1/2': isHorizontalCard }"
				/>
				<div
					class="flex flex-col gap-2"
					:class="{ 'w-1/2 justify-evenly': isHorizontalCard }"
				>
					<LazySkeleton
						class="h-6 rounded w-3/4"
						:class="{ 'h-12 w-full': isHorizontalCard }"
					/>
					<LazySkeleton
						class="h-4  rounded w-2/4"
						:class="{ 'h-10 w-2/3': isHorizontalCard }"
					/>
				</div>
			</div>
			<NuxtLinkLocale
				v-if="!loading"
				:title="product?.name"
				class="content group"
				:to="`/product/${product?.slug}`"
			>
				<div class="header flex bg-gray-100 p-2 relative items-center justify-center overflow-hidden max-md:max-h-36 min-w-full">
					<div class="flex h-full py-2 overflow-hidden items-center justify-center">
						<NuxtImg
							:src="image"
							:alt="product?.name"
							:title="product?.name"
							class="aspect-square object-contain"
							:sizes="{
								xs: '201px',
								sm: '250px',
								md: '630px',
							}"
							width="630px"
							height="630px"
							format="webp"
							fit="contain"
							provider="backend"
							:loading="imageLoading"
						/>
					</div>
					<div
						class="actions flex flex-col absolute gap-1 top-2 left-2 text-[#ADAFB6] group-hover:text-[#404553]"
					>
						<Button
							:size="'icon'"
							variant="icon"
							class="bg-white rounded border   hover:text-primary-600 hover:!border-primary-600 group-hover:border-[#404553] transition-colors duration-300"
							:class="{ '!bg-primary-600 !text-white !border-primary-600': isFav, 'border-0': isOutStock }"
							@click.prevent="onFavorite()"
						>
							<Icon
								:name="`${!isFav?'ui:heart':'ui:heart-fill'}`"
								:size="variant !== 'sm'?`19px`:'12px'"
								:class="{ 'text-white': isFav }"
							/>
						</Button>

						<Button
							:size="'icon'"
							variant="icon"
							class="bg-white  group rounded border  hover:text-primary-500 hover:!border-primary-500 group-hover:border-[#404553]"
							:class="{ '!bg-primary-600 !text-white': isCompare, 'border-0': isOutStock }"
							@click.prevent="onCompare()"
						>
							<Icon
								name="ui:card-cart"
								:size="variant !== 'sm'?`19px`:'12px'"
								:class="{ 'text-white': isCompare }"
							/>
						</Button>
					</div>
					<div
						v-if="isOutStock"
						class="absolute pointer-events-none left-0 top-0 w-full h-full bg-gray-100 bg-opacity-80 z-10 flex justify-center items-center"
					>
						<span class="out-stock-text">
							{{ $t('product.out-stock') }}
						</span>
					</div>
				</div>
				<div class="body">
					<div class="name">
						<slot
							name="title"
							v-bind="{ product }"
						>
							<h3 class="text-sm truncate-2-line leading-1">
								{{ product?.name }}
							</h3>
						</slot>
					</div>
					<div class="price">
						<div class="flex w-full p-2 bg-[#F3F9FC] justify-between items-center gap-2">
							<div
								v-if="!isOutStock"
								class="flex gap-2 items-center"
							>
								<span class="text-md font-bold text-green-700 max-sm:text-sm">
									{{ priceFormat(stockPrice) }}
								</span>
								<span
									v-if="discountAmount"
									class="text-xs font-semibold text-gray-500 line-through max-sm:text-xs max-sm:font-semibold"
								>
									{{ priceFormat(offerPrice) }}
								</span>
							</div>

							<div
								v-if="discountPercent"
								class="flex rounded-3xl items-center py-1 px-3 bg-orange-500 max-sm:px-1 max-sm:py-0.5"
							>
								<span class="text-white text-xs font-semibold text-nowrap max-sm:text-2xs">
									{{ $t('product.card-discount', { amount: discountPercent }) }}
								</span>
							</div>
						</div>
					</div>
				</div>
			</NuxtLinkLocale>
		</CardContent>
	</Card>
	<Modal
		v-if="!!hasCartConfirm"
		:title="$t('cart-list.add-item-title')"
		@close="hasCartConfirm = null"
	>
		<template #footer>
			<div class="flex w-full justify-end items-center gap-4">
				<Button
					variant="outline"
					@click.once="hasCartConfirm = null"
				>
					<span>{{ $t('cart-list.confirm-continue') }}</span>
				</Button>

				<Button as-child>
					<NuxtLinkLocale to="/cart">
						<span>{{ $t('cart-list.confirm-continue-pay') }}</span>
					</NuxtLinkLocale>
				</Button>
			</div>
		</template>

		<template #body>
			<div
				v-if="product?.productId"
				class="flex w-full px-4 justify-center items-center mb-4"
			>
				<DrawerCartListCard
					:product="product"
					:view-only="true"
				/>
			</div>
		</template>
	</Modal>
</template>

<style scoped lang="scss">
.content{
  @apply flex flex-col cursor-pointer;
}

.body {
  @apply flex flex-col h-full justify-between p-4;
}

.name {
  @apply flex w-full h-11 mb-2;
}

.price {
  @apply flex w-full;
}

.header{
  @apply relative z-0;
}
.out-stock-text{
  @apply text-base font-semibold drop-shadow-xl text-center;
}
.card-lg {
  @apply max-w-80 min-w-72;
  .header {
    @apply h-72;
    img {
      @apply w-8/12 aspect-square;
    }
  }

  .price {
    @apply h-16
  }
}

.card-md {
  .header {
    @apply h-36;
    img {
      @apply w-28
    }
  }

  .price {
    @apply h-11
  }

  .body{
    @apply max-sm:!px-2;
  }
}

.card-sm {
  @apply w-36;
  .header {
    @apply h-28;
    img {
      @apply h-full;
    }
  }

  .price {
    @apply hidden
  }

  .name {
    @apply mb-1.5;
  }

  .actions{
    @apply left-0 scale-75 top-0;
    button {
      @apply bg-gray-50 border border-gray-200;
    }
  }

  .out-stock-text{
    @apply text-sm;
  }
}

.card-horizontal {
  @apply w-full max-sm:max-h-32;
  .content {
    @apply flex-row items-center w-full ;
    .header {
      @apply min-w-48 h-36 max-sm:min-w-28;
      img{
        @apply w-28 ;
      }
    }
    .actions {
      @apply hidden
    }

    .body{
      @apply gap-2 justify-between w-full h-full;
      .price {
        @apply flex align-bottom
      }
    }
  }

}

.content:hover{

}
</style>
